<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Page not found - <PERSON><PERSON>'s Portfolio">
    <meta name="robots" content="noindex, nofollow">
    <title>404 - Page Not Found | <PERSON><PERSON>'s Portfolio</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --bg-primary: #0a0a0a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --accent-primary: #00ff88;
            --accent-secondary: #0066ff;
            --accent-tertiary: #ff0066;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .error-container {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .error-code {
            font-size: clamp(8rem, 20vw, 15rem);
            font-weight: 900;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 2rem;
            animation: glitch 2s ease-in-out infinite;
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }
        
        .error-message {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .error-actions {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 1rem 2rem;
            border-radius: 1rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 150px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            color: var(--bg-primary);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--accent-primary);
            border: 2px solid var(--accent-primary);
        }
        
        .btn-secondary:hover {
            background: var(--accent-primary);
            color: var(--bg-primary);
            transform: translateY(-2px);
        }
        
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.1;
        }
        
        .circuit-line {
            position: absolute;
            background: var(--accent-primary);
            animation: circuitFlow 4s linear infinite;
        }
        
        .circuit-line:nth-child(1) {
            top: 20%;
            left: 0;
            width: 100%;
            height: 2px;
            animation-delay: 0s;
        }
        
        .circuit-line:nth-child(2) {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            animation-delay: 1s;
        }
        
        .circuit-line:nth-child(3) {
            top: 80%;
            left: 0;
            width: 100%;
            height: 2px;
            animation-delay: 2s;
        }
        
        .circuit-line:nth-child(4) {
            top: 0;
            left: 20%;
            width: 2px;
            height: 100%;
            animation-delay: 0.5s;
        }
        
        .circuit-line:nth-child(5) {
            top: 0;
            left: 80%;
            width: 2px;
            height: 100%;
            animation-delay: 1.5s;
        }
        
        @keyframes glitch {
            0%, 100% { 
                transform: translate(0);
                filter: hue-rotate(0deg);
            }
            20% { 
                transform: translate(-2px, 2px);
                filter: hue-rotate(90deg);
            }
            40% { 
                transform: translate(-2px, -2px);
                filter: hue-rotate(180deg);
            }
            60% { 
                transform: translate(2px, 2px);
                filter: hue-rotate(270deg);
            }
            80% { 
                transform: translate(2px, -2px);
                filter: hue-rotate(360deg);
            }
        }
        
        @keyframes circuitFlow {
            0% {
                opacity: 0;
                transform: scaleX(0);
            }
            50% {
                opacity: 1;
                transform: scaleX(1);
            }
            100% {
                opacity: 0;
                transform: scaleX(0);
            }
        }
        
        @media (max-width: 768px) {
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="circuit-line"></div>
        <div class="circuit-line"></div>
        <div class="circuit-line"></div>
        <div class="circuit-line"></div>
        <div class="circuit-line"></div>
    </div>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">Page Not Found</h1>
        <p class="error-message">
            Oops! The page you're looking for seems to have wandered off into the digital void. 
            Don't worry, even the best circuits sometimes have loose connections!
        </p>
        <div class="error-actions">
            <a href="/" class="btn btn-primary">Back to Home</a>
            <a href="/#contact" class="btn btn-secondary">Contact Me</a>
        </div>
    </div>
    
    <script>
        // Add some interactive particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: #00ff88;
                border-radius: 50%;
                pointer-events: none;
                z-index: 1;
                left: ${Math.random() * 100}vw;
                top: 100vh;
                animation: floatUp ${3 + Math.random() * 3}s linear forwards;
            `;
            
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }
        
        // Add CSS for particle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes floatUp {
                to {
                    transform: translateY(-100vh) translateX(${Math.random() * 200 - 100}px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Create particles periodically
        setInterval(createParticle, 500);
        
        // Console easter egg
        console.log('🔧 404 Error - But hey, you found the console!');
        console.log('💡 Raihan says: "Every error is a learning opportunity!"');
    </script>
</body>
</html>
