<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="iconGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ff88;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0066ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff0066;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="#0a0a0a" stroke="url(#iconGrad)" stroke-width="3"/>
  
  <!-- Circuit pattern -->
  <path d="M20,50 L35,50 L40,40 L45,60 L60,50 L80,50" stroke="#00ff88" stroke-width="2" fill="none"/>
  <path d="M50,20 L50,35 L40,40 L60,45 L50,60 L50,80" stroke="#0066ff" stroke-width="2" fill="none"/>
  
  <!-- Connection points -->
  <circle cx="35" cy="50" r="3" fill="#00ff88"/>
  <circle cx="65" cy="50" r="3" fill="#0066ff"/>
  <circle cx="50" cy="35" r="3" fill="#ff0066"/>
  <circle cx="50" cy="65" r="3" fill="#00ff88"/>
  
  <!-- Central innovation symbol -->
  <circle cx="50" cy="50" r="12" fill="none" stroke="#00ff88" stroke-width="2"/>
  <polygon points="50,42 56,50 50,58 44,50" fill="#00ff88"/>
  <circle cx="50" cy="50" r="4" fill="#0066ff"/>
  
  <!-- Letter R -->
  <text x="50" y="58" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="url(#iconGrad)" text-anchor="middle">R</text>
</svg>
