<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON> (SK Raihan) - Electronics Engineering Student, Innovator & Maker. Founder of SKR Electronics Lab. Born to Build, Made to Innovate.">
    <meta name="keywords" content="Raihan, SK Raihan, SKR Electronics Lab, Electronics Engineering, Arduino, ESP, Computer Vision, Drones, Innovation, Maker, DIY Projects">
    <meta name="author" content="<PERSON><PERSON> (SK Raihan)">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#0a0a0a">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Raihan - Electronics Innovator & Maker">
    <meta property="og:description" content="Electronics Engineering Student, Innovator & Maker. Founder of SKR Electronics Lab. Born to Build, Made to Innovate.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.skrelectronicslab.com">
    <meta property="og:image" content="https://www.skrelectronicslab.com/assets/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@skrelectronics">
    <meta name="twitter:title" content="Raihan - Electronics Innovator & Maker">
    <meta name="twitter:description" content="Electronics Engineering Student, Innovator & Maker. Founder of SKR Electronics Lab. Born to Build, Made to Innovate.">
    <meta name="twitter:image" content="https://www.skrelectronicslab.com/assets/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/favicon-16x16.png">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="js/main.js" as="script">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/main.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Raihan",
        "alternateName": "SK Raihan",
        "jobTitle": "Electronics Engineering Student & Innovator",
        "worksFor": {
            "@type": "Organization",
            "name": "SKR Electronics Lab"
        },
        "url": "https://www.skrelectronicslab.com",
        "sameAs": [
            "https://instagram.com/skr_electronics_lab",
            "https://youtube.com/@skr_electronics_lab",
            "https://twitter.com/skrelectronics"
        ],
        "knowsAbout": ["Electronics", "Arduino", "ESP", "Computer Vision", "Drones", "Innovation", "DIY Projects"],
        "description": "Electronics Engineering Student, Innovator & Maker. Founder of SKR Electronics Lab. Born to Build, Made to Innovate."
    }
    </script>
    
    <title>Raihan - Electronics Innovator & Maker | SKR Electronics Lab</title>
</head>
<body class="dark-mode">
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scroll-progress"></div>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-animation">
            <div class="circuit-loader">
                <svg viewBox="0 0 100 100" class="circuit-svg">
                    <path class="circuit-path" d="M20,50 L40,50 L50,30 L60,70 L80,50" stroke="#00ff88" stroke-width="2" fill="none"/>
                    <circle class="pulse-dot" cx="20" cy="50" r="3" fill="#00ff88"/>
                </svg>
            </div>
            <p class="loading-text">Initializing Innovation...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">Raihan</span>
                <span class="logo-subtitle">SKR</span>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item"><a href="#home" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="#about" class="nav-link">About</a></li>
                <li class="nav-item"><a href="#skills" class="nav-link">Skills</a></li>
                <li class="nav-item"><a href="#projects" class="nav-link">Projects</a></li>
                <li class="nav-item"><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>
                <div class="hamburger" id="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-background">
                <div class="animated-bg">
                    <svg class="bg-svg" viewBox="0 0 1920 1080" preserveAspectRatio="xMidYMid slice">
                        <defs>
                            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00ff88;stop-opacity:0.1" />
                                <stop offset="50%" style="stop-color:#0066ff;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#ff0066;stop-opacity:0.1" />
                            </linearGradient>
                            <filter id="glow">
                                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                <feMerge> 
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>
                        </defs>
                        
                        <!-- Animated geometric shapes -->
                        <g class="floating-shapes">
                            <circle class="shape shape-1" cx="200" cy="200" r="50" fill="url(#grad1)" filter="url(#glow)"/>
                            <polygon class="shape shape-2" points="800,100 850,150 800,200 750,150" fill="#00ff88" opacity="0.1" filter="url(#glow)"/>
                            <rect class="shape shape-3" x="1400" y="300" width="80" height="80" fill="#0066ff" opacity="0.1" filter="url(#glow)" transform="rotate(45 1440 340)"/>
                            <circle class="shape shape-4" cx="1600" cy="600" r="30" fill="#ff0066" opacity="0.1" filter="url(#glow)"/>
                            <polygon class="shape shape-5" points="300,800 350,750 400,800 350,850" fill="#00ff88" opacity="0.1" filter="url(#glow)"/>
                        </g>
                        
                        <!-- Circuit-like connections -->
                        <g class="circuit-lines">
                            <path class="circuit-line line-1" d="M0,540 Q480,300 960,540 T1920,540" stroke="#00ff88" stroke-width="1" fill="none" opacity="0.3"/>
                            <path class="circuit-line line-2" d="M0,400 Q600,600 1200,400 T1920,400" stroke="#0066ff" stroke-width="1" fill="none" opacity="0.3"/>
                            <path class="circuit-line line-3" d="M0,680 Q720,200 1440,680 T1920,680" stroke="#ff0066" stroke-width="1" fill="none" opacity="0.3"/>
                        </g>
                    </svg>
                </div>
                
                <!-- Particle system -->
                <div class="particles" id="particles"></div>
            </div>
            
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="title-line">Hi, I'm</span>
                        <span class="title-name">Raihan</span>
                        <span class="title-subtitle">Electronics Innovator & Maker</span>
                    </h1>
                    <p class="hero-motto">
                        <span class="motto-text">Born to Build, Made to Innovate</span>
                    </p>
                    <div class="hero-buttons">
                        <a href="#projects" class="btn btn-primary">View My Work</a>
                        <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="avatar-container">
                        <svg class="avatar-svg" viewBox="0 0 300 300">
                            <!-- Creative avatar representation -->
                            <defs>
                                <radialGradient id="avatarGrad" cx="50%" cy="30%">
                                    <stop offset="0%" style="stop-color:#00ff88;stop-opacity:0.8" />
                                    <stop offset="100%" style="stop-color:#0066ff;stop-opacity:0.2" />
                                </radialGradient>
                            </defs>
                            
                            <!-- Background circle -->
                            <circle cx="150" cy="150" r="140" fill="url(#avatarGrad)" opacity="0.1"/>
                            
                            <!-- Circuit board pattern -->
                            <g class="circuit-pattern">
                                <path d="M50,150 L100,150 L120,130 L140,170 L180,150 L250,150" stroke="#00ff88" stroke-width="2" fill="none" opacity="0.6"/>
                                <path d="M150,50 L150,100 L130,120 L170,140 L150,180 L150,250" stroke="#0066ff" stroke-width="2" fill="none" opacity="0.6"/>
                                <circle cx="100" cy="150" r="5" fill="#00ff88"/>
                                <circle cx="200" cy="150" r="5" fill="#0066ff"/>
                                <circle cx="150" cy="100" r="5" fill="#ff0066"/>
                                <circle cx="150" cy="200" r="5" fill="#00ff88"/>
                            </g>
                            
                            <!-- Central innovation symbol -->
                            <g class="innovation-symbol">
                                <circle cx="150" cy="150" r="30" fill="none" stroke="#00ff88" stroke-width="3"/>
                                <polygon points="150,130 160,150 150,170 140,150" fill="#00ff88"/>
                                <circle cx="150" cy="150" r="8" fill="#0066ff"/>
                            </g>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Scroll indicator -->
            <div class="scroll-indicator">
                <div class="scroll-arrow">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <p class="scroll-text">Scroll to explore</p>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">About Me</h2>
                    <p class="section-subtitle">The story of an innovator</p>
                </div>
                
                <div class="about-content">
                    <div class="about-text">
                        <div class="story-timeline">
                            <div class="timeline-item" data-aos="fade-up">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h3>The Beginning</h3>
                                    <p>As an Electronics Engineering student from India, my journey began with a simple curiosity about how things work. This curiosity quickly evolved into a passion for building and innovating.</p>
                                </div>
                            </div>
                            
                            <div class="timeline-item" data-aos="fade-up" data-aos-delay="200">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h3>SKR Electronics Lab</h3>
                                    <p>I founded SKR Electronics Lab as a platform to share knowledge, create innovative projects, and inspire others to build amazing things. From Arduino projects to advanced computer vision systems.</p>
                                </div>
                            </div>
                            
                            <div class="timeline-item" data-aos="fade-up" data-aos-delay="400">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h3>Content Creation</h3>
                                    <p>Through YouTube and Instagram, I share my projects, tutorials, and insights with a growing community of makers and innovators worldwide.</p>
                                </div>
                            </div>
                            
                            <div class="timeline-item" data-aos="fade-up" data-aos-delay="600">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h3>Today & Beyond</h3>
                                    <p>Today, I continue to push boundaries in electronics, software development, and creative problem-solving. My mission is to inspire others to embrace the maker mindset.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="about-visual">
                        <div class="innovation-stats">
                            <div class="stat-item" data-aos="zoom-in">
                                <div class="stat-number" data-count="50">0</div>
                                <div class="stat-label">Projects Built</div>
                            </div>
                            <div class="stat-item" data-aos="zoom-in" data-aos-delay="200">
                                <div class="stat-number" data-count="10000">0</div>
                                <div class="stat-label">Community Members</div>
                            </div>
                            <div class="stat-item" data-aos="zoom-in" data-aos-delay="400">
                                <div class="stat-number" data-count="100">0</div>
                                <div class="stat-label">Tutorials Created</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section id="skills" class="skills-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Skills & Expertise</h2>
                    <p class="section-subtitle">Technologies and tools I work with</p>
                </div>

                <div class="skills-grid">
                    <div class="skill-category" data-aos="fade-up">
                        <div class="category-header">
                            <div class="category-icon">
                                <svg viewBox="0 0 100 100" class="skill-svg">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#00ff88" stroke-width="3"/>
                                    <path d="M30,50 L45,35 L55,45 L70,30" stroke="#00ff88" stroke-width="3" fill="none"/>
                                    <circle cx="35" cy="45" r="3" fill="#00ff88"/>
                                    <circle cx="50" cy="40" r="3" fill="#0066ff"/>
                                    <circle cx="65" cy="35" r="3" fill="#ff0066"/>
                                </svg>
                            </div>
                            <h3 class="category-title">Electronics & Hardware</h3>
                        </div>
                        <div class="skills-list">
                            <div class="skill-item">
                                <span class="skill-name">Arduino & ESP</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="95"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Circuit Design</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="90"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">PCB Design</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="85"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Embedded Systems</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="88"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="skill-category" data-aos="fade-up" data-aos-delay="200">
                        <div class="category-header">
                            <div class="category-icon">
                                <svg viewBox="0 0 100 100" class="skill-svg">
                                    <rect x="20" y="30" width="60" height="40" fill="none" stroke="#0066ff" stroke-width="3" rx="5"/>
                                    <path d="M30,45 L40,35 L50,45 L60,35 L70,45" stroke="#0066ff" stroke-width="2" fill="none"/>
                                    <circle cx="35" cy="55" r="2" fill="#0066ff"/>
                                    <circle cx="50" cy="55" r="2" fill="#00ff88"/>
                                    <circle cx="65" cy="55" r="2" fill="#ff0066"/>
                                </svg>
                            </div>
                            <h3 class="category-title">Software Development</h3>
                        </div>
                        <div class="skills-list">
                            <div class="skill-item">
                                <span class="skill-name">Python</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="92"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">JavaScript</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="88"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">C/C++</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="85"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Web Development</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="90"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="skill-category" data-aos="fade-up" data-aos-delay="400">
                        <div class="category-header">
                            <div class="category-icon">
                                <svg viewBox="0 0 100 100" class="skill-svg">
                                    <circle cx="50" cy="50" r="35" fill="none" stroke="#ff0066" stroke-width="3"/>
                                    <path d="M35,35 L65,35 L65,50 L50,65 L35,50 Z" fill="none" stroke="#ff0066" stroke-width="2"/>
                                    <circle cx="50" cy="45" r="5" fill="#ff0066"/>
                                    <path d="M45,55 L55,55" stroke="#ff0066" stroke-width="2"/>
                                </svg>
                            </div>
                            <h3 class="category-title">Specialized Skills</h3>
                        </div>
                        <div class="skills-list">
                            <div class="skill-item">
                                <span class="skill-name">Computer Vision</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="87"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Drone Technology</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="83"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">IoT Systems</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="89"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">3D Design & Printing</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="80"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Interactive Skills Showcase -->
                <div class="skills-showcase" data-aos="zoom-in" data-aos-delay="600">
                    <h3 class="showcase-title">Interactive Skills Demo</h3>
                    <div class="demo-container">
                        <div class="demo-item active" data-demo="electronics">
                            <div class="demo-icon">⚡</div>
                            <span>Electronics</span>
                        </div>
                        <div class="demo-item" data-demo="software">
                            <div class="demo-icon">💻</div>
                            <span>Software</span>
                        </div>
                        <div class="demo-item" data-demo="innovation">
                            <div class="demo-icon">🚀</div>
                            <span>Innovation</span>
                        </div>
                    </div>

                    <div class="demo-display">
                        <div class="demo-content active" id="demo-electronics">
                            <svg class="demo-svg" viewBox="0 0 300 200">
                                <defs>
                                    <linearGradient id="circuitGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#00ff88"/>
                                        <stop offset="100%" style="stop-color:#0066ff"/>
                                    </linearGradient>
                                </defs>
                                <g class="circuit-animation">
                                    <path d="M50,100 L100,100 L120,80 L140,120 L180,100 L250,100" stroke="url(#circuitGrad)" stroke-width="3" fill="none"/>
                                    <circle class="pulse-point" cx="100" cy="100" r="4" fill="#00ff88"/>
                                    <circle class="pulse-point" cx="180" cy="100" r="4" fill="#0066ff"/>
                                    <rect x="115" y="75" width="10" height="10" fill="#ff0066" transform="rotate(45 120 80)"/>
                                    <rect x="135" y="115" width="10" height="10" fill="#00ff88" transform="rotate(45 140 120)"/>
                                </g>
                            </svg>
                        </div>

                        <div class="demo-content" id="demo-software">
                            <svg class="demo-svg" viewBox="0 0 300 200">
                                <g class="code-animation">
                                    <rect x="50" y="50" width="200" height="100" fill="none" stroke="#0066ff" stroke-width="2" rx="10"/>
                                    <line x1="70" y1="80" x2="130" y2="80" stroke="#00ff88" stroke-width="2"/>
                                    <line x1="70" y1="100" x2="180" y2="100" stroke="#ff0066" stroke-width="2"/>
                                    <line x1="70" y1="120" x2="150" y2="120" stroke="#0066ff" stroke-width="2"/>
                                    <circle class="typing-cursor" cx="160" cy="120" r="2" fill="#00ff88"/>
                                </g>
                            </svg>
                        </div>

                        <div class="demo-content" id="demo-innovation">
                            <svg class="demo-svg" viewBox="0 0 300 200">
                                <g class="innovation-animation">
                                    <circle cx="150" cy="100" r="40" fill="none" stroke="#ff0066" stroke-width="3"/>
                                    <path d="M150,70 L160,90 L180,85 L165,105 L175,125 L150,115 L125,125 L135,105 L120,85 L140,90 Z" fill="#00ff88" opacity="0.7"/>
                                    <circle class="idea-spark" cx="150" cy="100" r="3" fill="#ff0066"/>
                                </g>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="projects-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Featured Projects</h2>
                    <p class="section-subtitle">Innovation in action - building the future</p>
                </div>

                <div class="projects-grid">
                    <div class="project-card featured" data-aos="zoom-in">
                        <div class="project-image">
                            <svg class="project-svg" viewBox="0 0 400 250">
                                <defs>
                                    <linearGradient id="projectGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#00ff88"/>
                                        <stop offset="100%" style="stop-color:#0066ff"/>
                                    </linearGradient>
                                </defs>
                                <rect width="400" height="250" fill="#1a1a1a"/>
                                <g class="security-visual">
                                    <circle cx="200" cy="125" r="60" fill="none" stroke="url(#projectGrad1)" stroke-width="3"/>
                                    <path d="M170,125 L190,145 L230,105" stroke="#00ff88" stroke-width="4" fill="none"/>
                                    <circle cx="200" cy="125" r="20" fill="rgba(0,255,136,0.2)"/>
                                    <text x="200" y="200" text-anchor="middle" fill="#00ff88" font-size="16" font-weight="bold">SECURITY</text>
                                </g>
                            </svg>
                        </div>
                        <div class="project-content">
                            <div class="project-header">
                                <h3 class="project-title">SKR Pentester</h3>
                                <div class="project-status">Featured</div>
                            </div>
                            <p class="project-description">
                                Advanced penetration testing toolkit for ethical security assessment.
                                Built with Python, featuring automated vulnerability scanning and reporting.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Python</span>
                                <span class="tech-tag">Cybersecurity</span>
                                <span class="tech-tag">Automation</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link primary">View Project</a>
                                <a href="#" class="project-link secondary">GitHub</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="project-image">
                            <svg class="project-svg" viewBox="0 0 400 250">
                                <rect width="400" height="250" fill="#1a1a1a"/>
                                <g class="fire-visual">
                                    <circle cx="200" cy="150" r="40" fill="rgba(255,0,102,0.3)"/>
                                    <path d="M200,110 Q180,130 190,150 Q200,140 210,150 Q220,130 200,110" fill="#ff0066"/>
                                    <circle cx="180" cy="180" r="15" fill="rgba(0,102,255,0.5)"/>
                                    <circle cx="220" cy="180" r="15" fill="rgba(0,102,255,0.5)"/>
                                    <text x="200" y="220" text-anchor="middle" fill="#ff0066" font-size="14" font-weight="bold">FIRE SAFETY</text>
                                </g>
                            </svg>
                        </div>
                        <div class="project-content">
                            <div class="project-header">
                                <h3 class="project-title">Smart Fire Extinguisher</h3>
                                <div class="project-status">IoT</div>
                            </div>
                            <p class="project-description">
                                Automated fire detection and suppression system using IoT sensors,
                                Arduino, and real-time monitoring capabilities.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Arduino</span>
                                <span class="tech-tag">IoT</span>
                                <span class="tech-tag">Sensors</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link primary">View Project</a>
                                <a href="#" class="project-link secondary">Demo</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="project-image">
                            <svg class="project-svg" viewBox="0 0 400 250">
                                <rect width="400" height="250" fill="#1a1a1a"/>
                                <g class="drone-visual">
                                    <ellipse cx="200" cy="125" rx="80" ry="20" fill="rgba(0,255,136,0.2)"/>
                                    <rect x="180" y="115" width="40" height="20" fill="#0066ff" rx="5"/>
                                    <circle cx="150" cy="100" r="12" fill="none" stroke="#00ff88" stroke-width="2"/>
                                    <circle cx="250" cy="100" r="12" fill="none" stroke="#00ff88" stroke-width="2"/>
                                    <circle cx="150" cy="150" r="12" fill="none" stroke="#00ff88" stroke-width="2"/>
                                    <circle cx="250" cy="150" r="12" fill="none" stroke="#00ff88" stroke-width="2"/>
                                    <text x="200" y="200" text-anchor="middle" fill="#0066ff" font-size="14" font-weight="bold">DRONE TECH</text>
                                </g>
                            </svg>
                        </div>
                        <div class="project-content">
                            <div class="project-header">
                                <h3 class="project-title">Custom Drone Build</h3>
                                <div class="project-status">Hardware</div>
                            </div>
                            <p class="project-description">
                                Custom-built quadcopter with advanced flight control systems,
                                FPV capabilities, and autonomous navigation features.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Flight Control</span>
                                <span class="tech-tag">FPV</span>
                                <span class="tech-tag">Autonomous</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link primary">View Project</a>
                                <a href="#" class="project-link secondary">Video</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card" data-aos="fade-up" data-aos-delay="600">
                        <div class="project-image">
                            <svg class="project-svg" viewBox="0 0 400 250">
                                <rect width="400" height="250" fill="#1a1a1a"/>
                                <g class="vision-visual">
                                    <rect x="150" y="100" width="100" height="60" fill="none" stroke="#ff0066" stroke-width="2" rx="10"/>
                                    <circle cx="175" cy="120" r="8" fill="rgba(255,0,102,0.5)"/>
                                    <circle cx="225" cy="120" r="8" fill="rgba(255,0,102,0.5)"/>
                                    <path d="M160,140 Q200,150 240,140" stroke="#ff0066" stroke-width="2" fill="none"/>
                                    <rect x="100" y="80" width="20" height="20" fill="rgba(0,255,136,0.3)"/>
                                    <rect x="280" y="80" width="20" height="20" fill="rgba(0,255,136,0.3)"/>
                                    <text x="200" y="200" text-anchor="middle" fill="#ff0066" font-size="14" font-weight="bold">COMPUTER VISION</text>
                                </g>
                            </svg>
                        </div>
                        <div class="project-content">
                            <div class="project-header">
                                <h3 class="project-title">Vision Recognition System</h3>
                                <div class="project-status">AI/ML</div>
                            </div>
                            <p class="project-description">
                                Advanced computer vision system for object detection and recognition
                                using machine learning algorithms and real-time processing.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">OpenCV</span>
                                <span class="tech-tag">Python</span>
                                <span class="tech-tag">Machine Learning</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link primary">View Project</a>
                                <a href="#" class="project-link secondary">GitHub</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card" data-aos="fade-up" data-aos-delay="800">
                        <div class="project-image">
                            <svg class="project-svg" viewBox="0 0 400 250">
                                <rect width="400" height="250" fill="#1a1a1a"/>
                                <g class="lab-visual">
                                    <rect x="100" y="100" width="200" height="80" fill="none" stroke="#0066ff" stroke-width="2" rx="10"/>
                                    <circle cx="150" cy="130" r="15" fill="rgba(0,255,136,0.3)"/>
                                    <circle cx="200" cy="130" r="15" fill="rgba(0,102,255,0.3)"/>
                                    <circle cx="250" cy="130" r="15" fill="rgba(255,0,102,0.3)"/>
                                    <path d="M120,160 L280,160" stroke="#00ff88" stroke-width="2"/>
                                    <text x="200" y="210" text-anchor="middle" fill="#0066ff" font-size="14" font-weight="bold">SKR ELECTRONICS LAB</text>
                                </g>
                            </svg>
                        </div>
                        <div class="project-content">
                            <div class="project-header">
                                <h3 class="project-title">SKR Electronics Lab</h3>
                                <div class="project-status">Platform</div>
                            </div>
                            <p class="project-description">
                                Educational platform and community hub for electronics enthusiasts,
                                featuring tutorials, projects, and innovative learning resources.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Education</span>
                                <span class="tech-tag">Community</span>
                                <span class="tech-tag">Innovation</span>
                            </div>
                            <div class="project-links">
                                <a href="https://www.skrelectronicslab.com" class="project-link primary">Visit Site</a>
                                <a href="#" class="project-link secondary">Learn More</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card" data-aos="fade-up" data-aos-delay="1000">
                        <div class="project-image">
                            <svg class="project-svg" viewBox="0 0 400 250">
                                <rect width="400" height="250" fill="#1a1a1a"/>
                                <g class="tools-visual">
                                    <rect x="150" y="80" width="100" height="100" fill="none" stroke="#00ff88" stroke-width="2" rx="10"/>
                                    <path d="M170,100 L180,110 L190,100 L200,110 L210,100 L220,110 L230,100" stroke="#00ff88" stroke-width="2" fill="none"/>
                                    <circle cx="200" cy="140" r="20" fill="rgba(0,255,136,0.2)"/>
                                    <path d="M185,140 L195,150 L215,130" stroke="#00ff88" stroke-width="3" fill="none"/>
                                    <text x="200" y="210" text-anchor="middle" fill="#00ff88" font-size="14" font-weight="bold">CREATIVE TOOLS</text>
                                </g>
                            </svg>
                        </div>
                        <div class="project-content">
                            <div class="project-header">
                                <h3 class="project-title">Creative Development Tools</h3>
                                <div class="project-status">Tools</div>
                            </div>
                            <p class="project-description">
                                Collection of custom development tools and utilities designed to
                                streamline the creative process and enhance productivity.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Productivity</span>
                                <span class="tech-tag">Automation</span>
                                <span class="tech-tag">Utilities</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link primary">Explore Tools</a>
                                <a href="#" class="project-link secondary">Download</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Filter -->
                <div class="project-filter" data-aos="fade-up" data-aos-delay="1200">
                    <h3 class="filter-title">Filter Projects</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">All Projects</button>
                        <button class="filter-btn" data-filter="hardware">Hardware</button>
                        <button class="filter-btn" data-filter="software">Software</button>
                        <button class="filter-btn" data-filter="iot">IoT</button>
                        <button class="filter-btn" data-filter="ai">AI/ML</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Get In Touch</h2>
                    <p class="section-subtitle">Let's build something amazing together</p>
                </div>

                <div class="contact-content">
                    <div class="contact-info" data-aos="fade-right">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <svg viewBox="0 0 100 100" class="icon-svg">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#00ff88" stroke-width="3"/>
                                    <path d="M30,40 L50,60 L70,40" stroke="#00ff88" stroke-width="3" fill="none"/>
                                    <rect x="25" y="35" width="50" height="30" fill="none" stroke="#00ff88" stroke-width="2" rx="5"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h3>Email</h3>
                                <p><span class="email-protected" data-email="<EMAIL>">Click to reveal email</span></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <svg viewBox="0 0 100 100" class="icon-svg">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#0066ff" stroke-width="3"/>
                                    <path d="M50,25 L50,75 M25,50 L75,50" stroke="#0066ff" stroke-width="3"/>
                                    <circle cx="50" cy="50" r="15" fill="none" stroke="#0066ff" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h3>Location</h3>
                                <p>India</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <svg viewBox="0 0 100 100" class="icon-svg">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#ff0066" stroke-width="3"/>
                                    <circle cx="50" cy="50" r="25" fill="none" stroke="#ff0066" stroke-width="2"/>
                                    <circle cx="50" cy="50" r="10" fill="#ff0066"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h3>Response Time</h3>
                                <p>Within 24 hours</p>
                            </div>
                        </div>

                        <div class="social-links">
                            <h3>Follow My Journey</h3>
                            <div class="social-grid">
                                <a href="https://instagram.com/skr_electronics_lab" target="_blank" rel="noopener noreferrer" class="social-link instagram">
                                    <svg viewBox="0 0 100 100" class="social-svg">
                                        <rect x="20" y="20" width="60" height="60" rx="15" fill="none" stroke="currentColor" stroke-width="4"/>
                                        <circle cx="50" cy="50" r="15" fill="none" stroke="currentColor" stroke-width="4"/>
                                        <circle cx="65" cy="35" r="3" fill="currentColor"/>
                                    </svg>
                                    <span>Instagram</span>
                                </a>

                                <a href="https://youtube.com/@skr_electronics_lab" target="_blank" rel="noopener noreferrer" class="social-link youtube">
                                    <svg viewBox="0 0 100 100" class="social-svg">
                                        <rect x="15" y="30" width="70" height="40" rx="10" fill="none" stroke="currentColor" stroke-width="4"/>
                                        <path d="M40,40 L60,50 L40,60 Z" fill="currentColor"/>
                                    </svg>
                                    <span>YouTube</span>
                                </a>

                                <a href="https://twitter.com/skrelectronics" target="_blank" rel="noopener noreferrer" class="social-link twitter">
                                    <svg viewBox="0 0 100 100" class="social-svg">
                                        <path d="M25,75 Q35,65 45,70 Q55,75 65,65 Q75,55 70,45 Q65,35 55,40 Q45,45 35,35 Q25,25 30,35 Q35,45 25,55 Z" fill="none" stroke="currentColor" stroke-width="4"/>
                                    </svg>
                                    <span>Twitter</span>
                                </a>

                                <a href="https://www.skrelectronicslab.com" target="_blank" rel="noopener noreferrer" class="social-link website">
                                    <svg viewBox="0 0 100 100" class="social-svg">
                                        <circle cx="50" cy="50" r="35" fill="none" stroke="currentColor" stroke-width="4"/>
                                        <path d="M20,50 Q50,20 80,50 Q50,80 20,50" fill="none" stroke="currentColor" stroke-width="2"/>
                                        <path d="M50,15 L50,85 M15,50 L85,50" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    <span>Website</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="contact-form-container" data-aos="fade-left">
                        <form class="contact-form" id="contact-form">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" id="name" name="name" required>
                                <span class="form-line"></span>
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" required>
                                <span class="form-line"></span>
                            </div>

                            <div class="form-group">
                                <label for="subject">Subject</label>
                                <input type="text" id="subject" name="subject" required>
                                <span class="form-line"></span>
                            </div>

                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                                <span class="form-line"></span>
                            </div>

                            <button type="submit" class="submit-btn">
                                <span class="btn-text">Send Message</span>
                                <span class="btn-loading">Sending...</span>
                                <svg class="btn-icon" viewBox="0 0 100 100">
                                    <path d="M20,50 L80,50 M60,30 L80,50 L60,70" stroke="currentColor" stroke-width="4" fill="none"/>
                                </svg>
                            </button>
                        </form>

                        <div class="form-success" id="form-success">
                            <svg class="success-icon" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#00ff88" stroke-width="4"/>
                                <path d="M30,50 L45,65 L70,35" stroke="#00ff88" stroke-width="4" fill="none"/>
                            </svg>
                            <h3>Message Sent!</h3>
                            <p>Thank you for reaching out. I'll get back to you soon!</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <span class="logo-text">Raihan</span>
                        <span class="logo-subtitle">SKR Electronics Lab</span>
                    </div>
                    <p class="footer-motto">Born to Build, Made to Innovate</p>
                </div>

                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Quick Links</h4>
                        <ul>
                            <li><a href="#home">Home</a></li>
                            <li><a href="#about">About</a></li>
                            <li><a href="#skills">Skills</a></li>
                            <li><a href="#projects">Projects</a></li>
                            <li><a href="#contact">Contact</a></li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4>Projects</h4>
                        <ul>
                            <li><a href="#">SKR Pentester</a></li>
                            <li><a href="#">Fire Extinguisher</a></li>
                            <li><a href="#">Drone Technology</a></li>
                            <li><a href="#">Computer Vision</a></li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4>Connect</h4>
                        <ul>
                            <li><a href="https://instagram.com/skr_electronics_lab" target="_blank">Instagram</a></li>
                            <li><a href="https://youtube.com/@skr_electronics_lab" target="_blank">YouTube</a></li>
                            <li><a href="https://twitter.com/skrelectronics" target="_blank">Twitter</a></li>
                            <li><a href="https://www.skrelectronicslab.com" target="_blank">Website</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Raihan (SK Raihan). All rights reserved.</p>
                <p>Built with ❤️ and lots of ☕</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/main.js"></script>
</body>
</html>
