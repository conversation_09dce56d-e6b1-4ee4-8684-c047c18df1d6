/**
 * Main JavaScript file for <PERSON><PERSON>'s Portfolio
 * Handles all interactive features, animations, and user interactions
 */

// ===== GLOBAL VARIABLES =====
let isLoading = true;
let currentTheme = 'dark';
let particles = [];
let mouseX = 0;
let mouseY = 0;

// ===== DOM ELEMENTS =====
const loadingScreen = document.getElementById('loading-screen');
const navbar = document.getElementById('navbar');
const themeToggle = document.getElementById('theme-toggle');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const particlesContainer = document.getElementById('particles');

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Welcome to Raihan\'s Portfolio!');
    console.log('💡 Born to Build, Made to Innovate');
    console.log('🔧 Inspect the code to see the magic behind the scenes!');
    
    initializeApp();
});

function initializeApp() {
    // Initialize all components
    initializeLoading();
    initializeNavigation();
    initializeTheme();
    initializeParticles();
    initializeScrollAnimations();
    initializeMouseTracking();
    initializeCounters();
    initializeSkills();
    initializeProjects();
    initializeContact();
    initializeAdvancedInteractions();
    initializeScrollProgress();
    initializePerformanceOptimizations();

    // Hide loading screen after initialization
    setTimeout(() => {
        hideLoadingScreen();
    }, 2000);
}

// ===== LOADING SCREEN =====
function initializeLoading() {
    // Add loading screen animations
    const loadingText = document.querySelector('.loading-text');
    const messages = [
        'Initializing Innovation...',
        'Loading Creativity...',
        'Preparing Magic...',
        'Almost Ready...'
    ];
    
    let messageIndex = 0;
    const messageInterval = setInterval(() => {
        if (messageIndex < messages.length - 1) {
            messageIndex++;
            loadingText.textContent = messages[messageIndex];
        } else {
            clearInterval(messageInterval);
        }
    }, 500);
}

function hideLoadingScreen() {
    loadingScreen.classList.add('hidden');
    isLoading = false;
    
    // Remove loading screen from DOM after animation
    setTimeout(() => {
        loadingScreen.remove();
    }, 500);
}

// ===== NAVIGATION =====
function initializeNavigation() {
    // Hamburger menu toggle
    hamburger.addEventListener('click', toggleMobileMenu);
    
    // Navigation link clicks
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavClick);
    });
    
    // Scroll-based navigation highlighting
    window.addEventListener('scroll', updateActiveNavLink);
    
    // Navbar background on scroll
    window.addEventListener('scroll', updateNavbarBackground);
}

function toggleMobileMenu() {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
    document.body.classList.toggle('menu-open');
}

function handleNavClick(e) {
    e.preventDefault();
    const targetId = e.target.getAttribute('href');
    const targetSection = document.querySelector(targetId);
    
    if (targetSection) {
        // Close mobile menu if open
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
        document.body.classList.remove('menu-open');
        
        // Smooth scroll to section
        targetSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let currentSection = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

function updateNavbarBackground() {
    const scrolled = window.scrollY > 50;
    navbar.classList.toggle('scrolled', scrolled);
}

// ===== THEME SYSTEM =====
function initializeTheme() {
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        currentTheme = savedTheme;
        document.body.classList.toggle('light-mode', currentTheme === 'light');
        updateThemeIcon();
    }
    
    // Theme toggle event listener
    themeToggle.addEventListener('click', toggleTheme);
}

function toggleTheme() {
    currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.body.classList.toggle('light-mode', currentTheme === 'light');
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
    
    // Add transition effect
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    setTimeout(() => {
        document.body.style.transition = '';
    }, 300);
}

function updateThemeIcon() {
    const themeIcon = themeToggle.querySelector('.theme-icon');
    themeIcon.textContent = currentTheme === 'dark' ? '☀️' : '🌙';
}

// ===== PARTICLE SYSTEM =====
function initializeParticles() {
    createParticles();
    animateParticles();
}

function createParticles() {
    const particleCount = 50;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Random starting position
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 8 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
        
        // Random color
        const colors = ['#00ff88', '#0066ff', '#ff0066'];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];
        
        particlesContainer.appendChild(particle);
        particles.push(particle);
    }
}

function animateParticles() {
    // Particles are animated via CSS, but we can add interactive effects here
    particles.forEach((particle, index) => {
        // Add mouse interaction
        particle.addEventListener('mouseenter', () => {
            particle.style.transform = 'scale(2)';
            particle.style.boxShadow = '0 0 20px currentColor';
        });
        
        particle.addEventListener('mouseleave', () => {
            particle.style.transform = 'scale(1)';
            particle.style.boxShadow = 'none';
        });
    });
}

// ===== MOUSE TRACKING =====
function initializeMouseTracking() {
    document.addEventListener('mousemove', handleMouseMove);
}

function handleMouseMove(e) {
    mouseX = e.clientX;
    mouseY = e.clientY;
    
    // Update CSS custom properties for mouse-based effects
    document.documentElement.style.setProperty('--mouse-x', mouseX + 'px');
    document.documentElement.style.setProperty('--mouse-y', mouseY + 'px');
    
    // Parallax effect for hero background
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        if (rect.top <= window.innerHeight && rect.bottom >= 0) {
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const deltaX = (mouseX - centerX) / rect.width;
            const deltaY = (mouseY - centerY) / rect.height;
            
            const shapes = document.querySelectorAll('.floating-shapes .shape');
            shapes.forEach((shape, index) => {
                const intensity = (index + 1) * 10;
                shape.style.transform = `translate(${deltaX * intensity}px, ${deltaY * intensity}px)`;
            });
        }
    }
}

// ===== SCROLL ANIMATIONS =====
function initializeScrollAnimations() {
    // Intersection Observer for scroll-triggered animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(handleIntersection, observerOptions);
    
    // Observe all elements with data-aos attribute
    const animatedElements = document.querySelectorAll('[data-aos]');
    animatedElements.forEach(el => observer.observe(el));
}

function handleIntersection(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const element = entry.target;
            const animationType = element.getAttribute('data-aos');
            const delay = element.getAttribute('data-aos-delay') || 0;
            
            setTimeout(() => {
                element.classList.add('aos-animate');
                triggerAnimation(element, animationType);
            }, parseInt(delay));
        }
    });
}

function triggerAnimation(element, type) {
    switch (type) {
        case 'fade-up':
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            break;
        case 'zoom-in':
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
            break;
        default:
            element.style.opacity = '1';
    }
}

// ===== COUNTER ANIMATIONS =====
function initializeCounters() {
    const counters = document.querySelectorAll('[data-count]');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => counterObserver.observe(counter));
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-count'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        // Format number with commas for large numbers
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// ===== SKILLS SECTION =====
function initializeSkills() {
    // Initialize skill progress bars
    initializeSkillBars();

    // Initialize interactive demo
    initializeSkillsDemo();
}

function initializeSkillBars() {
    const skillBars = document.querySelectorAll('.skill-progress');

    const skillObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const progress = progressBar.getAttribute('data-progress');

                setTimeout(() => {
                    progressBar.style.width = progress + '%';
                }, 200);

                skillObserver.unobserve(progressBar);
            }
        });
    }, { threshold: 0.5 });

    skillBars.forEach(bar => skillObserver.observe(bar));
}

function initializeSkillsDemo() {
    const demoItems = document.querySelectorAll('.demo-item');
    const demoContents = document.querySelectorAll('.demo-content');

    demoItems.forEach(item => {
        item.addEventListener('click', () => {
            const demoType = item.getAttribute('data-demo');

            // Remove active class from all items and contents
            demoItems.forEach(i => i.classList.remove('active'));
            demoContents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked item and corresponding content
            item.classList.add('active');
            document.getElementById(`demo-${demoType}`).classList.add('active');

            // Trigger animation restart
            restartDemoAnimation(demoType);
        });
    });
}

function restartDemoAnimation(demoType) {
    const demoContent = document.getElementById(`demo-${demoType}`);
    const animatedElements = demoContent.querySelectorAll('[class*="animation"] *');

    animatedElements.forEach(element => {
        element.style.animation = 'none';
        element.offsetHeight; // Trigger reflow
        element.style.animation = null;
    });
}

// ===== PROJECTS SECTION =====
function initializeProjects() {
    // Initialize project filtering
    initializeProjectFilter();

    // Initialize project card interactions
    initializeProjectCards();
}

function initializeProjectFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const projectCards = document.querySelectorAll('.project-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.getAttribute('data-filter');

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Filter projects
            filterProjects(filter, projectCards);
        });
    });
}

function filterProjects(filter, cards) {
    cards.forEach(card => {
        const cardTech = Array.from(card.querySelectorAll('.tech-tag')).map(tag =>
            tag.textContent.toLowerCase()
        );
        const cardStatus = card.querySelector('.project-status').textContent.toLowerCase();

        let shouldShow = filter === 'all';

        if (!shouldShow) {
            switch (filter) {
                case 'hardware':
                    shouldShow = cardTech.some(tech =>
                        tech.includes('arduino') ||
                        tech.includes('hardware') ||
                        tech.includes('flight control') ||
                        cardStatus.includes('hardware')
                    );
                    break;
                case 'software':
                    shouldShow = cardTech.some(tech =>
                        tech.includes('python') ||
                        tech.includes('automation') ||
                        tech.includes('tools') ||
                        cardStatus.includes('tools')
                    );
                    break;
                case 'iot':
                    shouldShow = cardTech.some(tech =>
                        tech.includes('iot') ||
                        tech.includes('sensors') ||
                        cardStatus.includes('iot')
                    );
                    break;
                case 'ai':
                    shouldShow = cardTech.some(tech =>
                        tech.includes('machine learning') ||
                        tech.includes('opencv') ||
                        cardStatus.includes('ai/ml')
                    );
                    break;
            }
        }

        // Animate card visibility
        if (shouldShow) {
            card.style.display = 'block';
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        } else {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.display = 'none';
            }, 300);
        }
    });
}

function initializeProjectCards() {
    const projectCards = document.querySelectorAll('.project-card');

    projectCards.forEach(card => {
        // Add hover effects for project links
        const links = card.querySelectorAll('.project-link');
        links.forEach(link => {
            link.addEventListener('mouseenter', () => {
                link.style.transform = 'translateY(-2px) scale(1.02)';
            });

            link.addEventListener('mouseleave', () => {
                link.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click analytics (placeholder)
        card.addEventListener('click', (e) => {
            if (!e.target.closest('.project-link')) {
                const projectTitle = card.querySelector('.project-title').textContent;
                console.log(`Project viewed: ${projectTitle}`);

                // Add visual feedback
                card.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    card.style.transform = '';
                }, 150);
            }
        });
    });
}

// ===== CONTACT SECTION =====
function initializeContact() {
    // Initialize email protection
    initializeEmailProtection();

    // Initialize contact form
    initializeContactForm();
}

function initializeEmailProtection() {
    const emailElements = document.querySelectorAll('.email-protected');

    emailElements.forEach(element => {
        const email = element.getAttribute('data-email');

        element.addEventListener('click', () => {
            element.textContent = email;
            element.style.cursor = 'text';
            element.style.textDecoration = 'none';

            // Add copy to clipboard functionality
            navigator.clipboard.writeText(email).then(() => {
                const originalText = element.textContent;
                element.textContent = 'Email copied to clipboard!';
                element.style.color = 'var(--accent-primary)';

                setTimeout(() => {
                    element.textContent = originalText;
                    element.style.color = '';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                element.textContent = email;
            });
        });
    });
}

function initializeContactForm() {
    const form = document.getElementById('contact-form');
    const submitBtn = form.querySelector('.submit-btn');
    const successMessage = document.getElementById('form-success');

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Show loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        // Get form data
        const formData = new FormData(form);
        const data = {
            name: formData.get('name'),
            email: formData.get('email'),
            subject: formData.get('subject'),
            message: formData.get('message')
        };

        // Simulate form submission (replace with actual endpoint)
        try {
            await simulateFormSubmission(data);

            // Show success message
            form.style.display = 'none';
            successMessage.classList.add('show');

            // Log for analytics
            console.log('Contact form submitted:', data);

            // Reset form after delay
            setTimeout(() => {
                form.reset();
                form.style.display = 'block';
                successMessage.classList.remove('show');
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }, 5000);

        } catch (error) {
            console.error('Form submission error:', error);
            alert('Sorry, there was an error sending your message. Please try again.');
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
        }
    });

    // Add real-time validation
    const inputs = form.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

function simulateFormSubmission(data) {
    return new Promise((resolve, reject) => {
        // Simulate network delay
        setTimeout(() => {
            // Simulate success (90% success rate)
            if (Math.random() > 0.1) {
                resolve(data);
            } else {
                reject(new Error('Network error'));
            }
        }, 2000);
    });
}

function validateField(e) {
    const field = e.target;
    const value = field.value.trim();

    // Remove existing error
    clearFieldError(e);

    let isValid = true;
    let errorMessage = '';

    // Validation rules
    switch (field.type) {
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
            break;
        case 'text':
            if (value.length < 2) {
                isValid = false;
                errorMessage = 'This field must be at least 2 characters long';
            }
            break;
        default:
            if (value.length < 10) {
                isValid = false;
                errorMessage = 'Message must be at least 10 characters long';
            }
    }

    if (!isValid) {
        showFieldError(field, errorMessage);
    }

    return isValid;
}

function showFieldError(field, message) {
    const formGroup = field.closest('.form-group');

    // Create error element if it doesn't exist
    let errorElement = formGroup.querySelector('.field-error');
    if (!errorElement) {
        errorElement = document.createElement('span');
        errorElement.className = 'field-error';
        formGroup.appendChild(errorElement);
    }

    errorElement.textContent = message;
    field.style.borderBottomColor = 'var(--accent-tertiary)';
}

function clearFieldError(e) {
    const field = e.target;
    const formGroup = field.closest('.form-group');
    const errorElement = formGroup.querySelector('.field-error');

    if (errorElement) {
        errorElement.remove();
    }

    field.style.borderBottomColor = '';
}

// ===== ADVANCED INTERACTIONS =====
function initializeAdvancedInteractions() {
    initializeSmoothScrolling();
    initializeParallaxEffects();
    initializeMouseInteractions();
    initializeKeyboardShortcuts();
    initializeConsoleEasterEggs();
}

function initializeSmoothScrolling() {
    // Smooth scroll for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const headerOffset = 80;
                const elementPosition = targetElement.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });

                // Update active nav item
                updateActiveNavItem(targetId);
            }
        });
    });

    // Update active nav on scroll
    window.addEventListener('scroll', throttle(updateActiveNavOnScroll, 100));
}

function updateActiveNavItem(activeId) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${activeId}`) {
            link.classList.add('active');
        }
    });
}

function updateActiveNavOnScroll() {
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            updateActiveNavItem(sectionId);
        }
    });
}

function initializeParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.hero-background, .floating-shape');

    window.addEventListener('scroll', throttle(() => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    }, 16));
}

function initializeMouseInteractions() {
    // Mouse tracking for hero section
    const hero = document.querySelector('.hero-section');
    const heroBackground = document.querySelector('.hero-background');

    if (hero && heroBackground) {
        hero.addEventListener('mousemove', throttle((e) => {
            const rect = hero.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;

            const moveX = (x - 0.5) * 20;
            const moveY = (y - 0.5) * 20;

            heroBackground.style.transform = `translate(${moveX}px, ${moveY}px)`;
        }, 16));

        hero.addEventListener('mouseleave', () => {
            heroBackground.style.transform = 'translate(0, 0)';
        });
    }

    // Interactive cursor effect
    initializeCursorEffect();
}

function initializeCursorEffect() {
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.innerHTML = '<div class="cursor-dot"></div><div class="cursor-ring"></div>';
    document.body.appendChild(cursor);

    let mouseX = 0, mouseY = 0;
    let cursorX = 0, cursorY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    function animateCursor() {
        cursorX += (mouseX - cursorX) * 0.1;
        cursorY += (mouseY - cursorY) * 0.1;

        cursor.style.left = cursorX + 'px';
        cursor.style.top = cursorY + 'px';

        requestAnimationFrame(animateCursor);
    }

    animateCursor();

    // Cursor interactions
    const interactiveElements = document.querySelectorAll('a, button, .project-card, .skill-item');

    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            cursor.classList.add('cursor-hover');
        });

        element.addEventListener('mouseleave', () => {
            cursor.classList.remove('cursor-hover');
        });
    });
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for quick navigation
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            showQuickNavigation();
        }

        // Escape to close modals/overlays
        if (e.key === 'Escape') {
            closeAllOverlays();
        }

        // Theme toggle with 'T'
        if (e.key === 't' || e.key === 'T') {
            if (!e.target.matches('input, textarea')) {
                toggleTheme();
            }
        }
    });
}

function showQuickNavigation() {
    // Create quick navigation overlay
    const overlay = document.createElement('div');
    overlay.className = 'quick-nav-overlay';
    overlay.innerHTML = `
        <div class="quick-nav-content">
            <h3>Quick Navigation</h3>
            <div class="quick-nav-items">
                <a href="#home" class="quick-nav-item">🏠 Home</a>
                <a href="#about" class="quick-nav-item">👨‍💻 About</a>
                <a href="#skills" class="quick-nav-item">⚡ Skills</a>
                <a href="#projects" class="quick-nav-item">🚀 Projects</a>
                <a href="#contact" class="quick-nav-item">📧 Contact</a>
            </div>
            <p class="quick-nav-hint">Press Escape to close</p>
        </div>
    `;

    document.body.appendChild(overlay);

    // Close on click outside
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            overlay.remove();
        }
    });

    // Close on navigation
    overlay.querySelectorAll('.quick-nav-item').forEach(item => {
        item.addEventListener('click', () => {
            overlay.remove();
        });
    });
}

function closeAllOverlays() {
    const overlays = document.querySelectorAll('.quick-nav-overlay');
    overlays.forEach(overlay => overlay.remove());
}

function initializeConsoleEasterEggs() {
    console.log('%c🚀 Welcome to Raihan\'s Portfolio!', 'color: #00ff88; font-size: 20px; font-weight: bold;');
    console.log('%c⚡ Born to Build, Made to Innovate', 'color: #0066ff; font-size: 16px;');
    console.log('%c🔧 Built with passion and lots of coffee ☕', 'color: #ff0066; font-size: 14px;');
    console.log('%c💡 Try these keyboard shortcuts:', 'color: #ffffff; font-size: 14px;');
    console.log('%c   • Ctrl/Cmd + K: Quick Navigation', 'color: #b0b0b0; font-size: 12px;');
    console.log('%c   • T: Toggle Theme', 'color: #b0b0b0; font-size: 12px;');
    console.log('%c   • Konami Code: Special surprise 🎮', 'color: #b0b0b0; font-size: 12px;');

    // Add some fun console commands
    window.raihan = {
        about: () => console.log('Electronics Engineering student & founder of SKR Electronics Lab'),
        skills: () => console.log('Arduino/ESP, Computer Vision, Drones, Creative Tools, Content Creation'),
        contact: () => console.log('Email: <EMAIL>'),
        motto: () => console.log('Born to Build, Made to Innovate'),
        surprise: () => {
            console.log('%c🎉 You found the secret command!', 'color: #00ff88; font-size: 16px;');
            document.body.style.animation = 'rainbow 2s ease-in-out';
            setTimeout(() => {
                document.body.style.animation = '';
            }, 2000);
        }
    };

    console.log('%c🎯 Try typing: raihan.about(), raihan.skills(), raihan.surprise()', 'color: #00ff88; font-size: 12px;');
}

// ===== SCROLL PROGRESS =====
function initializeScrollProgress() {
    const progressBar = document.getElementById('scroll-progress');

    window.addEventListener('scroll', throttle(() => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        progressBar.style.width = scrollPercent + '%';
    }, 16));
}

// ===== PERFORMANCE OPTIMIZATIONS =====
function initializePerformanceOptimizations() {
    // Lazy loading for images and heavy content
    initializeLazyLoading();

    // Preload critical resources
    preloadCriticalResources();

    // Optimize animations based on device capabilities
    optimizeAnimations();

    // Initialize service worker for caching (if available)
    initializeServiceWorker();
}

function initializeLazyLoading() {
    const lazyElements = document.querySelectorAll('.lazy-load');

    if ('IntersectionObserver' in window) {
        const lazyObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');
                    lazyObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px'
        });

        lazyElements.forEach(element => {
            lazyObserver.observe(element);
        });
    } else {
        // Fallback for older browsers
        lazyElements.forEach(element => {
            element.classList.add('loaded');
        });
    }
}

function preloadCriticalResources() {
    // Preload critical images
    const criticalImages = [
        'assets/favicon.svg'
    ];

    criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });
}

function optimizeAnimations() {
    // Reduce animations on low-end devices
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2 ||
                          /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isLowEndDevice) {
        document.documentElement.style.setProperty('--transition-normal', '0.2s');
        document.documentElement.style.setProperty('--transition-slow', '0.3s');

        // Disable heavy animations
        const heavyAnimations = document.querySelectorAll('.floating-shape, .particle');
        heavyAnimations.forEach(element => {
            element.style.animation = 'none';
        });
    }
}

function initializeServiceWorker() {
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered: ', registration);
                })
                .catch(registrationError => {
                    console.log('SW registration failed: ', registrationError);
                });
        });
    }
}

// ===== ANALYTICS & MONITORING =====
function trackPageView() {
    // Placeholder for analytics tracking
    console.log('Page view tracked:', {
        url: window.location.href,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        viewport: {
            width: window.innerWidth,
            height: window.innerHeight
        }
    });
}

function trackInteraction(action, element) {
    // Placeholder for interaction tracking
    console.log('Interaction tracked:', {
        action,
        element: element.tagName + (element.className ? '.' + element.className : ''),
        timestamp: new Date().toISOString()
    });
}

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
    console.error('JavaScript error:', {
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno,
        error: e.error
    });
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Debounce scroll and resize events
window.addEventListener('scroll', debounce(updateActiveNavLink, 10));
window.addEventListener('scroll', throttle(updateNavbarBackground, 10));
window.addEventListener('resize', debounce(() => {
    // Handle resize events
    console.log('Window resized');
}, 250));

// ===== EASTER EGGS =====
// Console art and messages
console.log(`
    ╔══════════════════════════════════════╗
    ║        Welcome to Raihan's           ║
    ║         Portfolio Website!           ║
    ║                                      ║
    ║    🚀 Born to Build, Made to Innovate ║
    ║                                      ║
    ║    Built with ❤️ and lots of ☕      ║
    ╚══════════════════════════════════════╝
`);

// Konami code easter egg
let konamiCode = [];
const konamiSequence = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65]; // ↑↑↓↓←→←→BA

document.addEventListener('keydown', (e) => {
    konamiCode.push(e.keyCode);
    if (konamiCode.length > konamiSequence.length) {
        konamiCode.shift();
    }
    
    if (konamiCode.join(',') === konamiSequence.join(',')) {
        triggerEasterEgg();
        konamiCode = [];
    }
});

function triggerEasterEgg() {
    console.log('🎉 Konami Code activated! You found the secret!');
    
    // Add special effect
    document.body.style.animation = 'rainbow 2s ease-in-out';
    setTimeout(() => {
        document.body.style.animation = '';
    }, 2000);
    
    // Show special message
    const message = document.createElement('div');
    message.innerHTML = '🎉 Secret Unlocked! You\'re a true explorer! 🎉';
    message.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #00ff88, #0066ff);
        color: white;
        padding: 20px 40px;
        border-radius: 20px;
        font-size: 1.2rem;
        font-weight: bold;
        z-index: 10000;
        animation: bounceIn 0.5s ease-out;
    `;
    
    document.body.appendChild(message);
    setTimeout(() => {
        message.remove();
    }, 3000);
}

// Add rainbow animation for easter egg
const style = document.createElement('style');
style.textContent = `
    @keyframes rainbow {
        0% { filter: hue-rotate(0deg); }
        100% { filter: hue-rotate(360deg); }
    }
    
    @keyframes bounceIn {
        0% { transform: translate(-50%, -50%) scale(0); }
        50% { transform: translate(-50%, -50%) scale(1.1); }
        100% { transform: translate(-50%, -50%) scale(1); }
    }
`;
document.head.appendChild(style);
