/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Mode Colors (Default) */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --accent-primary: #00ff88;
    --accent-secondary: #0066ff;
    --accent-tertiary: #ff0066;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --glow-color: rgba(0, 255, 136, 0.3);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 4rem;
    --spacing-xl: 6rem;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 1rem;
    --radius-lg: 2rem;
    
    /* Z-index */
    --z-loading: 9999;
    --z-nav: 1000;
    --z-modal: 2000;
}

/* Light Mode Variables */
.light-mode {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --glow-color: rgba(0, 255, 136, 0.2);
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading);
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-animation {
    text-align: center;
}

.circuit-loader {
    width: 100px;
    height: 100px;
    margin: 0 auto var(--spacing-md);
}

.circuit-svg {
    width: 100%;
    height: 100%;
    animation: rotate 2s linear infinite;
}

.circuit-path {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    animation: drawPath 2s ease-in-out infinite;
}

.pulse-dot {
    animation: pulse 1s ease-in-out infinite;
}

.loading-text {
    color: var(--accent-primary);
    font-size: 1.2rem;
    font-weight: 500;
    animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes rotate {
    to { transform: rotate(360deg); }
}

@keyframes drawPath {
    to { stroke-dashoffset: 0; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: var(--z-nav);
    transition: all var(--transition-normal);
}

.light-mode .navbar {
    background: rgba(255, 255, 255, 0.9);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    line-height: 1;
}

.logo-subtitle {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
    letter-spacing: 2px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-md);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-primary);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    font-size: 1.2rem;
}

.theme-toggle:hover {
    border-color: var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
    transform: scale(1.1);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all var(--transition-fast);
    border-radius: 2px;
}

.hamburger.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: var(--spacing-xl) var(--spacing-md);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.bg-svg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.floating-shapes .shape {
    animation: float 6s ease-in-out infinite;
}

.shape-1 { animation-delay: 0s; }
.shape-2 { animation-delay: 1s; }
.shape-3 { animation-delay: 2s; }
.shape-4 { animation-delay: 3s; }
.shape-5 { animation-delay: 4s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(90deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
    75% { transform: translateY(-30px) rotate(270deg); }
}

.circuit-lines .circuit-line {
    stroke-dasharray: 10, 5;
    animation: flowCircuit 4s linear infinite;
}

.line-1 { animation-delay: 0s; }
.line-2 { animation-delay: 1s; }
.line-3 { animation-delay: 2s; }

@keyframes flowCircuit {
    to { stroke-dashoffset: -100; }
}

.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: particleFloat 8s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

.hero-content {
    max-width: 1200px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    z-index: 1;
}

.hero-text {
    animation: slideInLeft 1s ease-out;
}

.hero-title {
    margin-bottom: var(--spacing-md);
}

.title-line {
    display: block;
    font-size: 1.5rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin-bottom: var(--spacing-xs);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.title-name {
    display: block;
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 800;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xs);
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.title-subtitle {
    display: block;
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hero-motto {
    margin-bottom: var(--spacing-lg);
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.motto-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--accent-primary);
    position: relative;
    display: inline-block;
}

.motto-text::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    animation: expandWidth 1s ease-out 1.5s both;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 100%; }
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    animation: fadeInUp 0.8s ease-out 1s both;
}

.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 150px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: var(--bg-primary);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px var(--glow-color);
}

.btn-secondary {
    background: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
}

.btn-secondary:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px var(--glow-color);
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: slideInRight 1s ease-out;
}

.avatar-container {
    width: 300px;
    height: 300px;
    position: relative;
}

.avatar-svg {
    width: 100%;
    height: 100%;
    animation: rotateAvatar 20s linear infinite;
}

.circuit-pattern {
    animation: pulseCircuit 3s ease-in-out infinite;
}

.innovation-symbol {
    animation: glowPulse 2s ease-in-out infinite;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotateAvatar {
    to { transform: rotate(360deg); }
}

@keyframes pulseCircuit {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

@keyframes glowPulse {
    0%, 100% { filter: drop-shadow(0 0 5px var(--accent-primary)); }
    50% { filter: drop-shadow(0 0 20px var(--accent-primary)); }
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-md);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    animation: fadeInUp 1s ease-out 1.5s both;
}

.scroll-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.scroll-arrow span {
    display: block;
    width: 2px;
    height: 20px;
    background: var(--accent-primary);
    margin: 2px 0;
    animation: scrollArrow 2s ease-in-out infinite;
}

.scroll-arrow span:nth-child(2) { animation-delay: 0.2s; }
.scroll-arrow span:nth-child(3) { animation-delay: 0.4s; }

@keyframes scrollArrow {
    0%, 100% { opacity: 0.3; transform: scaleY(0.5); }
    50% { opacity: 1; transform: scaleY(1); }
}

.scroll-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--bg-secondary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-lg);
        transition: left var(--transition-normal);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .avatar-container {
        width: 250px;
        height: 250px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-sm);
    }

    .hero-section {
        padding: var(--spacing-lg) var(--spacing-sm);
    }

    .title-name {
        font-size: clamp(2.5rem, 10vw, 4rem);
    }

    .hero-buttons {
        gap: var(--spacing-sm);
    }

    .btn {
        min-width: 120px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: var(--spacing-xl) var(--spacing-md);
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 255, 136, 0.02) 0%,
        rgba(0, 102, 255, 0.02) 50%,
        rgba(255, 0, 102, 0.02) 100%);
    pointer-events: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

.story-timeline {
    position: relative;
}

.story-timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, var(--accent-primary), var(--accent-secondary));
}

.timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--transition-slow);
}

.timeline-item.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.timeline-marker {
    position: absolute;
    left: 12px;
    top: 0;
    width: 16px;
    height: 16px;
    background: var(--accent-primary);
    border-radius: 50%;
    border: 4px solid var(--bg-secondary);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.timeline-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-xs);
}

.timeline-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1.1rem;
}

.innovation-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    position: sticky;
    top: 100px;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    opacity: 0;
    transform: scale(0.8);
}

.stat-item.aos-animate {
    opacity: 1;
    transform: scale(1);
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 30px var(--shadow-color);
    border-color: var(--accent-primary);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== ANIMATION UTILITIES ===== */
[data-aos] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

[data-aos="fade-up"] {
    transform: translateY(30px);
}

[data-aos="zoom-in"] {
    transform: scale(0.8);
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* ===== RESPONSIVE ABOUT SECTION ===== */
@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .innovation-stats {
        position: static;
        flex-direction: row;
        overflow-x: auto;
        padding: var(--spacing-md);
    }

    .stat-item {
        min-width: 150px;
        flex-shrink: 0;
    }

    .timeline-item {
        padding-left: 40px;
    }

    .story-timeline::before {
        left: 15px;
    }

    .timeline-marker {
        left: 7px;
    }
}

@media (max-width: 480px) {
    .about-section {
        padding: var(--spacing-lg) var(--spacing-sm);
    }

    .innovation-stats {
        flex-direction: column;
    }

    .stat-item {
        min-width: auto;
    }

    .stat-number {
        font-size: 2.5rem;
    }
}

/* ===== SKILLS SECTION ===== */
.skills-section {
    padding: var(--spacing-xl) var(--spacing-md);
    background: var(--bg-primary);
    position: relative;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.skill-category {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
}

.skill-category.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.skill-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px var(--shadow-color);
    border-color: var(--accent-primary);
}

.category-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--border-color);
    transition: all var(--transition-normal);
}

.skill-category:hover .category-icon {
    border-color: var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
    transform: scale(1.1);
}

.skill-svg {
    width: 30px;
    height: 30px;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.skills-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.skill-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.skill-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.skill-bar {
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 4px;
    width: 0;
    transition: width 1.5s ease-out;
    position: relative;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Skills Showcase */
.skills-showcase {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    text-align: center;
    opacity: 0;
    transform: scale(0.9);
    transition: all var(--transition-slow);
}

.skills-showcase.aos-animate {
    opacity: 1;
    transform: scale(1);
}

.showcase-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-lg);
}

.demo-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.demo-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    background: var(--bg-tertiary);
}

.demo-item:hover,
.demo-item.active {
    border-color: var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
    transform: translateY(-3px);
}

.demo-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
}

.demo-item span {
    font-weight: 600;
    color: var(--text-primary);
}

.demo-display {
    height: 200px;
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.demo-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.demo-content.active {
    opacity: 1;
}

.demo-svg {
    width: 100%;
    height: 100%;
    max-width: 300px;
}

/* Demo Animations */
.circuit-animation .pulse-point {
    animation: pulse 2s ease-in-out infinite;
}

.code-animation .typing-cursor {
    animation: blink 1s ease-in-out infinite;
}

.innovation-animation .idea-spark {
    animation: sparkle 3s ease-in-out infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
}

/* ===== PROJECTS SECTION ===== */
.projects-section {
    padding: var(--spacing-xl) var(--spacing-md);
    background: var(--bg-secondary);
    position: relative;
}

.projects-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 50%, rgba(0, 255, 136, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 102, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255, 0, 102, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.project-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
    position: relative;
}

.project-card.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--shadow-color);
    border-color: var(--accent-primary);
}

.project-card.featured {
    grid-column: span 2;
}

.project-card.featured .project-content {
    padding: var(--spacing-lg);
}

.project-image {
    height: 200px;
    overflow: hidden;
    position: relative;
    background: var(--bg-tertiary);
}

.project-svg {
    width: 100%;
    height: 100%;
    transition: transform var(--transition-normal);
}

.project-card:hover .project-svg {
    transform: scale(1.05);
}

.project-content {
    padding: var(--spacing-md);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.project-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.project-status {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    white-space: nowrap;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.tech-tag {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.tech-tag:hover {
    background: rgba(0, 255, 136, 0.1);
    border-color: var(--accent-primary);
    color: var(--accent-primary);
}

.project-links {
    display: flex;
    gap: var(--spacing-sm);
}

.project-link {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-normal);
    text-align: center;
    flex: 1;
    position: relative;
    overflow: hidden;
}

.project-link.primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: var(--bg-primary);
}

.project-link.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--glow-color);
}

.project-link.secondary {
    background: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
}

.project-link.secondary:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: translateY(-2px);
}

/* Project Filter */
.project-filter {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--transition-slow);
}

.project-filter.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.filter-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-md);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.filter-btn {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.filter-btn:hover,
.filter-btn.active {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: var(--bg-primary);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--glow-color);
}

/* Project Animations */
.security-visual circle {
    animation: securityPulse 3s ease-in-out infinite;
}

.fire-visual path {
    animation: flicker 2s ease-in-out infinite;
}

.drone-visual circle {
    animation: propellerSpin 1s linear infinite;
}

.vision-visual circle {
    animation: visionScan 2s ease-in-out infinite;
}

.lab-visual circle {
    animation: labPulse 2s ease-in-out infinite;
}

.tools-visual path {
    animation: toolsGlow 3s ease-in-out infinite;
}

@keyframes securityPulse {
    0%, 100% { stroke-width: 3; opacity: 1; }
    50% { stroke-width: 5; opacity: 0.7; }
}

@keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes propellerSpin {
    to { transform: rotate(360deg); }
}

@keyframes visionScan {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

@keyframes labPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

@keyframes toolsGlow {
    0%, 100% { stroke-width: 2; }
    50% { stroke-width: 4; }
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: var(--spacing-xl) var(--spacing-md);
    background: var(--bg-primary);
    position: relative;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

.contact-info {
    opacity: 0;
    transform: translateX(-30px);
    transition: all var(--transition-slow);
}

.contact-info.aos-animate {
    opacity: 1;
    transform: translateX(0);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px var(--shadow-color);
    border-color: var(--accent-primary);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--border-color);
    transition: all var(--transition-normal);
}

.contact-item:hover .contact-icon {
    border-color: var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
    transform: scale(1.1);
}

.icon-svg {
    width: 30px;
    height: 30px;
}

.contact-details h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 1rem;
}

.email-protected {
    cursor: pointer;
    color: var(--accent-primary);
    text-decoration: underline;
    transition: color var(--transition-fast);
}

.email-protected:hover {
    color: var(--accent-secondary);
}

.social-links {
    margin-top: var(--spacing-xl);
}

.social-links h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-md);
}

.social-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 2px solid var(--border-color);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.social-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px var(--shadow-color);
}

.social-link.instagram:hover {
    border-color: #E4405F;
    background: rgba(228, 64, 95, 0.1);
    color: #E4405F;
}

.social-link.youtube:hover {
    border-color: #FF0000;
    background: rgba(255, 0, 0, 0.1);
    color: #FF0000;
}

.social-link.twitter:hover {
    border-color: #1DA1F2;
    background: rgba(29, 161, 242, 0.1);
    color: #1DA1F2;
}

.social-link.website:hover {
    border-color: var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
    color: var(--accent-primary);
}

.social-svg {
    width: 30px;
    height: 30px;
}

.social-link span {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Contact Form */
.contact-form-container {
    opacity: 0;
    transform: translateX(30px);
    transition: all var(--transition-slow);
}

.contact-form-container.aos-animate {
    opacity: 1;
    transform: translateX(0);
}

.contact-form {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.form-group {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid var(--border-color);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    transition: all var(--transition-normal);
    outline: none;
}

.form-group input:focus,
.form-group textarea:focus {
    border-bottom-color: var(--accent-primary);
}

.form-group input:focus + .form-line,
.form-group textarea:focus + .form-line {
    width: 100%;
}

.form-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    transition: width var(--transition-normal);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: var(--bg-primary);
    border: none;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px var(--glow-color);
}

.submit-btn:active {
    transform: translateY(0);
}

.btn-loading {
    display: none;
}

.submit-btn.loading .btn-text {
    display: none;
}

.submit-btn.loading .btn-loading {
    display: inline;
}

.btn-icon {
    width: 20px;
    height: 20px;
    transition: transform var(--transition-normal);
}

.submit-btn:hover .btn-icon {
    transform: translateX(5px);
}

.form-success {
    display: none;
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 2px solid var(--accent-primary);
}

.form-success.show {
    display: block;
    animation: successSlideIn 0.5s ease-out;
}

.success-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-md);
}

.form-success h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-sm);
}

.form-success p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.field-error {
    color: var(--accent-tertiary);
    font-size: 0.85rem;
    margin-top: var(--spacing-xs);
    display: block;
    animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes successSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xl) var(--spacing-md) var(--spacing-md);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.footer-logo .logo-text {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.footer-logo .logo-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 1px;
    margin-top: var(--spacing-xs);
}

.footer-motto {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-primary);
    font-style: italic;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-primary);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section li {
    margin-bottom: var(--spacing-xs);
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    padding-left: 15px;
}

.footer-section a::before {
    content: '→';
    position: absolute;
    left: 0;
    opacity: 0;
    transform: translateX(-10px);
    transition: all var(--transition-fast);
    color: var(--accent-primary);
}

.footer-section a:hover {
    color: var(--accent-primary);
    padding-left: 20px;
}

.footer-section a:hover::before {
    opacity: 1;
    transform: translateX(0);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.footer-bottom p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN - CONTACT & FOOTER ===== */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .social-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .contact-section {
        padding: var(--spacing-lg) var(--spacing-sm);
    }

    .contact-form {
        padding: var(--spacing-md);
    }

    .footer {
        padding: var(--spacing-lg) var(--spacing-sm) var(--spacing-md);
    }
}

/* ===== SKILLS SECTION RESPONSIVE ===== */
@media (max-width: 768px) {
    .skills-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .demo-container {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .demo-item {
        flex-direction: row;
        justify-content: center;
    }
}

/* ===== PROJECTS SECTION RESPONSIVE ===== */
@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .project-card.featured {
        grid-column: span 1;
    }

    .filter-buttons {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 200px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .projects-section {
        padding: var(--spacing-lg) var(--spacing-sm);
    }

    .project-content {
        padding: var(--spacing-sm);
    }

    .project-links {
        flex-direction: column;
    }

    .project-tech {
        justify-content: center;
    }
}

/* ===== ADVANCED INTERACTIONS ===== */
.custom-cursor {
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

.cursor-dot {
    width: 8px;
    height: 8px;
    background: var(--accent-primary);
    border-radius: 50%;
    position: absolute;
    top: -4px;
    left: -4px;
}

.cursor-ring {
    width: 40px;
    height: 40px;
    border: 2px solid var(--accent-primary);
    border-radius: 50%;
    position: absolute;
    top: -20px;
    left: -20px;
    transition: all 0.3s ease;
}

.custom-cursor.cursor-hover .cursor-ring {
    transform: scale(1.5);
    border-color: var(--accent-secondary);
}

.quick-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: overlayFadeIn 0.3s ease-out;
}

.quick-nav-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.quick-nav-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-lg);
}

.quick-nav-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.quick-nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.quick-nav-item:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: translateX(5px);
}

.quick-nav-hint {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
}

@keyframes rainbow {
    0% { filter: hue-rotate(0deg); }
    25% { filter: hue-rotate(90deg); }
    50% { filter: hue-rotate(180deg); }
    75% { filter: hue-rotate(270deg); }
    100% { filter: hue-rotate(360deg); }
}

/* Navigation Active States */
.nav-link.active {
    color: var(--accent-primary);
    position: relative;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-primary);
    border-radius: 1px;
}

/* Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    z-index: 1000;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    width: 0%;
    transition: width 0.1s ease;
}

/* Enhanced Hover Effects */
.enhanced-hover {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.enhanced-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.enhanced-hover:hover::before {
    left: 100%;
}

/* Mobile Cursor Hide */
@media (max-width: 768px) {
    .custom-cursor {
        display: none;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .custom-cursor,
    .parallax-element,
    .floating-shape {
        animation: none !important;
        transform: none !important;
    }

    .enhanced-hover::before {
        display: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #ffffff;
        --shadow-color: rgba(255, 255, 255, 0.3);
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* GPU Acceleration for animations */
.floating-shape,
.particle,
.project-card,
.skill-item,
.contact-item,
.custom-cursor {
    will-change: transform;
    transform: translateZ(0);
}

/* Optimize font loading */
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local('Inter Regular'), local('Inter-Regular');
}

/* Critical CSS - Above the fold styles */
.hero-section,
.nav-container,
.loading-screen {
    contain: layout style paint;
}

/* Lazy loading optimization */
.lazy-load {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.lazy-load.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Print styles */
@media print {
    .nav-container,
    .loading-screen,
    .scroll-indicator,
    .custom-cursor,
    .floating-shape,
    .particle {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero-section,
    .about-section,
    .skills-section,
    .projects-section,
    .contact-section {
        page-break-inside: avoid;
        margin-bottom: 2rem;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* Focus indicators */
*:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--accent-primary);
    color: var(--bg-primary);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    font-weight: 600;
}

.skip-link:focus {
    top: 6px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    .project-card,
    .skill-item,
    .contact-item {
        border: 2px solid currentColor;
    }

    .btn,
    .project-link,
    .social-link {
        border: 2px solid currentColor;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-shape,
    .particle,
    .circuit-animation {
        animation: none !important;
    }
}

/* Dark mode media query support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0a0a0a;
        --bg-secondary: #1a1a1a;
        --bg-tertiary: #2a2a2a;
        --text-primary: #ffffff;
        --text-secondary: #b0b0b0;
    }
}

/* Light mode media query support */
@media (prefers-color-scheme: light) {
    body:not(.dark-mode) {
        --bg-primary: #ffffff;
        --bg-secondary: #f8f9fa;
        --bg-tertiary: #e9ecef;
        --text-primary: #212529;
        --text-secondary: #6c757d;
        --border-color: #dee2e6;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --glow-color: rgba(0, 102, 255, 0.2);
    }
}
